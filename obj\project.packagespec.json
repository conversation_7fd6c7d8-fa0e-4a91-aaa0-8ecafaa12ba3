﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Documents\\augment-projects\\winform-jiance\\winform-jiance\\winform-jiance\\winform-jiance.csproj","projectName":"winform-jiance","projectPath":"C:\\Users\\<USER>\\Documents\\augment-projects\\winform-jiance\\winform-jiance\\winform-jiance\\winform-jiance.csproj","outputPath":"C:\\Users\\<USER>\\Documents\\augment-projects\\winform-jiance\\winform-jiance\\winform-jiance\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0-windows"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0-windows7.0":{"targetAlias":"net8.0-windows","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0-windows7.0":{"targetAlias":"net8.0-windows","imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"},"Microsoft.WindowsDesktop.App.WindowsForms":{"privateAssets":"none"}},"runtimeIdentifierGraphPath":"C:\\Users\\<USER>\\.dotnet\\sdk\\8.0.406/PortableRuntimeIdentifierGraph.json"}}